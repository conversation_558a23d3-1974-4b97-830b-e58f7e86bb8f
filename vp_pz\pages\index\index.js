// vp/vp_sc/pages/index/index.js
var app = getApp();
var vp = require('../../resource/js/vp.js');

const innerAudioContext = wx.createInnerAudioContext();

Page({

        /**
         * 页面的初始数据
         */
        data: {
                signed:null,
                _signModal:false,

                _shareModal:false,

                addmy:false,
                noticed:0,// 我上次关闭的公告的ID，当前公告时间戳大于该数值才显示

                followed:null,
                _followedModal:false,

                cats:null,

                _searchModal:false,
                search:'',      // 搜索框中的内容
                searching:'', // 当前搜索的内容

                cat:'',
                start: 0,
                more: 1,
                list: null,
                is_loading_more: false,
                
                // 当前播放的喇叭
                horn:null,
                horn_audio_playing:false,

                // 当前查看的商户
                puber:null,

                // 管理员
                _shieldModal:false,

                currentSlideIndex: 0,
                prevSlideIndex: 0,

                // 头部遮挡效果控制
                navOpacity: 0, // 模糊头部的透明度 (0-1)
        },



        /**
         * 生命周期函数--监听页面加载
         */
        onLoad: function (options) {
                console.log(this);
                var self = this;

                var aid = '';
                var fuid = '';
                var stid = '';
                if (options.scene) { // 扫码进入
                  var scene = options.scene.split('_');
                  aid = scene[0] ? scene[0] : '';
                  fuid = scene[1] ? scene[1] : '';
                  stid = scene[2] ? scene[2] : '';
                }else{
                    aid = options.aid ? options.aid : '';
                    fuid = options.fuid ? options.fuid : '';
                    stid = options.stid ? options.stid : '';
                }

                // 如果没有传入aid，则默认进入上次的aid，上次的aid在app.mdInit函数中更新保存
                if(!aid){
                        aid=wx.getStorageSync('VP_AID');
                }


                self.setData({
                    aid:aid,
                    fuid:fuid,
                    stid:stid
                });

                console.log("==获取当前用户信息==");
                app.util.getUserInfo(function (response) {
                        console.log("==获取当前用户信息1==");
                        console.log(response);
                        self.setData({
                                userInfo: response
                        });
                        console.log("==应用初始化：应用配置/应用中用户信息/当前城市信息==");
                        app.mdInit(function (cfg,mine,area) {
                                self.setData({
                                        cfg: cfg,
                                        mine:mine,
                                        area:area
                                });
                                console.log(cfg);
                                console.log(mine);
                                console.log(area);


                                console.log("==加载首页数据==");
                                self.main_load(false);


                                if(!aid){
                                        console.log("==无指定城市时，获取当前用户位置自动切换对应城市==");
                                        // 如果没有aid，则发起位置定位
                                        wx.getFuzzyLocation({
                                                type: 'wgs84',
                                                success(res) {
                                                        // 定位成功，获取最近的店，并提示是否切换
                                                        //self.main_load(false);
                
                                                        app.util.request({
                                                                url: 'Index/location',
                                                                showLoading:false,
                                                                data: {
                                                                        m: app.mdName,
                                                                        //cmd:'location',
                                                                        lat:res.latitude,
                                                                        lng:res.longitude
                                                                },
                                                                success(res2) {
                                                                        var data = res2.data.data;
                                                                        console.log("==无指定城市时，定位到用户当前位置所在城市==");
                                                                        console.log(data);

                                                                        // 如果定位城市与当前城市不符，则推荐用户切换
                                                                        if(data.id!=area.id){
                                                                                wx.showModal({
                                                                                        title: '切换城市',
                                                                                        content: '您当前位于“'+data.name+'”',
                                                                                        cancelText:'其他城市',
                                                                                        confirmText:'确认进入',
                                                                                        success (ret) {
                                                                                                if (ret.confirm) {
                                                                                                        wx.reLaunch({
                                                                                                                //url: app.mdName + '/pages/index/index?aid='+data.id
                                                                                                                url: '../index/index?aid='+data.id
                                                                                                        });
                                                                                                }else{
                                                                                                        wx.navigateTo({
                                                                                                                url: '../index/areas'
                                                                                                        })
                                                                                                }
                                                                                        }
                                                                                });
                                                                        }
                                                                }
                                                        })
                                                },
                                                fail(res) {
                                                        console.log("fail");
                                                        console.log(res);
                                                        // 定位失败，获取默认的店，并提示是否切换
                                                        //self.main_load(false);
                                                }
                                        });
                                }
                        },{
                                aid:self.data.aid,
                                fuid:self.data.fuid,
                                stid:self.data.fuid
                        },true);
                });


                var menu = wx.getMenuButtonBoundingClientRect();
                this.setData({
                    menu:menu
                });
                console.log("menu==>");
                console.log(menu);
                

        },

        // 轮播图切换
        onSwiperChange: function(e) {
                const current = e.detail.current;
                const prev = this.data.currentSlideIndex;
                this.setData({
                        prevSlideIndex: prev,
                        currentSlideIndex: current
                });
        },

        // 顶部导航栏初始化完毕
        onNavBarAttached:function(e){
                this.setData({
                        statusHeight: e.detail.statusHeight,
                        navHeight: e.detail.navHeight,
                        navBarHeight: e.detail.navBarHeight
                });
                console.log("--------------------------------" + e.detail.navBarHeight);
                },

        onReady: function () {

  
                var self = this;

                var addmy = wx.getStorageSync('vp_pz_sharer_addmy');
                this.setData({
                        addmy:addmy?false:true
                });

                var noticed = wx.getStorageSync('vp_pz_noticed');
                if(noticed>0){
                        this.setData({
                                noticed:noticed
                        });  
                }
  
        },

        /**
         * 生命周期函数--监听页面显示
         */
        onShow: function () {
                var self=this;

                console.log("----");
                console.log(self.data.aid);

                if(this.data.main_loaded){
                        this.main_load(false);
                }
        },

        /**
         * 生命周期函数--监听页面隐藏
         */
        onHide: function () {

        },

        /**
         * 生命周期函数--监听页面卸载
         */
        onUnload: function () {

        },

        /**
         * 页面滚动监听 - 控制头部遮挡效果
         */
        onPageScroll: function(e) {
                const scrollTop = e.scrollTop;
                const threshold = 100; // 滚动阈值，超过这个值开始显示模糊头部

                // 计算透明度，0-1之间
                let opacity = 0;
                if (scrollTop > threshold) {
                        opacity = Math.min((scrollTop - threshold) / 100, 1);
                }

                // 更新头部透明度
                if (Math.abs(this.data.navOpacity - opacity) > 0.01) {
                        this.setData({
                                navOpacity: opacity
                        });
                }
        },


        onSlideTap:function(e){
                var slide = this.data.slides[e.currentTarget.dataset.index];
                if(!slide){
                        return;
                }
                if(slide.stype==1){
                        wx.navigateTo({
                          url: slide.stype_link,
                        });
                }else if(slide.stype==2){
                        wx.navigateTo({
                                url: 'html?cmd=slide&id='+slide.id
                        });
                }else if(slide.stype==3){
                        console.log(slide.stype_link);
                        var link=slide.stype_link.split(":");
                        console.log(link);
                        wx.navigateToMiniProgram({
                                appId: link[0],
                                path: link[1]
                        });
                }else if(slide.stype==4){
                        console.log(slide.stype_link);
                        var link=slide.stype_link.split(":");
                        console.log(link);
                        wx.openChannelsActivity({
                                finderUserName: link[0],
                                feedId: link[1]
                        });
                }
        },

        
        onNav2Tap:function(e){
                var nav = this.data.nav2s[e.currentTarget.dataset.index];
                if(!nav){
                        return;
                }
                if(nav.stype==1){
                        wx.navigateTo({
                          url: nav.stype_link,
                        });
                }else if(nav.stype==2){
                        wx.navigateTo({
                                url: 'html?cmd=nav&id='+nav.id
                        });
                }else if(nav.stype==3){
                        console.log(nav.stype_link);
                        var link=nav.stype_link.split(":");
                        console.log(link);
                        wx.navigateToMiniProgram({
                                appId: link[0],
                                path: link[1]
                        });
                }else if(nav.stype==4){
                        console.log(nav.stype_link);
                        var link=nav.stype_link.split(":");
                        console.log(link);
                        wx.openChannelsActivity({
                                finderUserName: link[0],
                                feedId: link[1]
                        });
                }
        },

        onNavTap:function(e){
                var nav = this.data.navs[e.currentTarget.dataset.index];
                if(!nav){
                        return;
                }
                if(nav.stype==1){
                        wx.navigateTo({
                          url: nav.stype_link,
                        });
                }else if(nav.stype==2){
                        wx.navigateTo({
                                url: 'html?cmd=nav&id='+nav.id
                        });
                }else if(nav.stype==3){
                        console.log(nav.stype_link);
                        var link=nav.stype_link.split(":");
                        console.log(link);
                        wx.navigateToMiniProgram({
                                appId: link[0],
                                path: link[1]
                        });
                }else if(nav.stype==4){
                        console.log(nav.stype_link);
                        var link=nav.stype_link.split(":");
                        console.log(link);
                        wx.openChannelsActivity({
                                finderUserName: link[0],
                                feedId: link[1]
                        });
                }
        },


        /**
         * 分享
        */
        onShareAppMessage: function () {
                console.log(this.getSharePath());
                return {
                        title: this.data.cfg.share_title?this.data.cfg.share_title:this.data.cfg.name,
                        imageUrl: this.data.cfg.share_image_url,
                        path: this.getSharePath()
                }
        },
        onShareTimeline:function(){
            return {
                title: this.data.cfg.share_title?this.data.cfg.share_title:this.data.cfg.name,
                imageUrl: this.data.cfg.logo_url,
                query:  'aid='+this.data.area.id+'&fuid=' + this.data.mine.id
            }
        },
        getSharePath:function(){
                var page = app.mdName + '/pages/index/index';
                var ps = '?aid='+this.data.area.id+'&fuid=' + this.data.mine.id
                return page + ps;
        },

        showShareModal(){
                this.setData({_shareModal:true});
        },

        // 切换区域
        toAreas:function(){
                wx.navigateTo({
                        url: '../index/areas',
                })
        },

        // 前往医院
        toHospital:function(e){
                wx.navigateTo({
                        url: '../index/hospital?hid='+e.currentTarget.dataset.hid,
                })
        },


        // 页面数据加载
        main_load(showLoading) {
                const self = this;
                //console.log("self.data.like "+self.data.like);
                app.util.request({
                        url: 'Index/index',
                        showLoading: showLoading ? true : false,
                        data: {
                                m: app.mdName,
                                aid:self.data.area.id,
                                need_address: 1 // 请求后端返回地址信息
                        },
                        complete(res) {
                        wx.stopPullDownRefresh();
                        },
                        success(res) {
                                var data = res.data.data;
                                
                                // 设置基本数据，不包括医院列表
                                self.setData({
                                        main_loaded: true,
                                        now:data.now,
                                        slides:data.slides,
                                        nav2s:data.nav2s,
                                        navs:data.navs,
                                        my_staff:data.my_staff
                                });
                                
                                // 处理医院数据，确保地址字段存在
                                if (data.hospitals && data.hospitals.length > 0) {
                                    // 创建一个计数器，用于跟踪已处理的医院数量
                                    let processedCount = 0;
                                    const totalHospitals = data.hospitals.length;
                                    
                                    // 为每个医院获取详细信息
                                    data.hospitals.forEach((hospital, index) => {
                                        // 从医院详情API获取地址信息
                                        app.util.request({
                                            url: 'Hospital/index',
                                            showLoading: false,
                                            data: {
                                                m: app.mdName,
                                                aid: self.data.area.id,
                                                hid: hospital.id
                                            },
                                            success(detailRes) {
                                                if (detailRes.data.code == 1 && detailRes.data.data && detailRes.data.data.hospital) {
                                                    const hospitalDetail = detailRes.data.data.hospital;
                                                    
                                                    // 更新医院地址信息
                                                    data.hospitals[index].city = hospitalDetail.city || '';
                                                    data.hospitals[index].district = hospitalDetail.district || '';
                                                    data.hospitals[index].address = hospitalDetail.address || '';
                                                }
                                                
                                                // 增加计数器
                                                processedCount++;
                                                
                                                // 当所有医院都处理完毕，更新UI
                                                if (processedCount === totalHospitals) {
                                                    self.setData({
                                                        hospitals: data.hospitals
                                                    });
                                                }
                                            },
                                            fail() {
                                                // 增加计数器，即使失败也继续处理
                                                processedCount++;
                                                
                                                // 当所有医院都处理完毕，更新UI
                                                if (processedCount === totalHospitals) {
                                                    self.setData({
                                                        hospitals: data.hospitals
                                                    });
                                                }
                                            }
                                        });
                                    });
                                } else {
                                    // 如果没有医院数据，直接设置空数组
                                    self.setData({
                                        hospitals: []
                                    });
                                }
                        }
                })
        },

        makePhoneCall:function(e){
                wx.makePhoneCall({
                        phoneNumber: e.currentTarget.dataset.tel
                });
        },


        closeAddmy(){
                this.setData({
                        addmy:false
                });
                wx.setStorage({
                        key:"vp_pz_sharer_addmy",
                        data:true
                });
        },

        
})