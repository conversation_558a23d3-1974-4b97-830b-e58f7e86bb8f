// vp_cai/pages/index/areas.js
var app = getApp();
var vp = require('../../resource/js/vp.js');

Page({

    /**
     * 页面的初始数据
     */
    data: {

    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        console.log(this);
        var self = this;

        app.util.getUserInfo(function (response) {
            self.setData({
                    userInfo: response
            });
            app.mdInit(function (cfg,mine,area) {
                    self.setData({
                            cfg: cfg,
                            mine:mine,
                            area:area
                    });
   
                    self.main_load(false);
            });
        });

    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    
    // 页面数据加载
    main_load(showLoading) {
        const self = this;
        //console.log("self.data.like "+self.data.like);
        app.util.request({
                url: 'Index/staffs',
                showLoading: showLoading ? true : false,
                data: {
                        m: app.mdName,
                        aid:self.data.area.id
                },
                success(res) {
                        var data = res.data.data;
                        self.setData({
                                main_loaded: true,
                                now:data.now,
                                staffs:data.staffs,
                                my_staff:data.my_staff
                        });
                }
        })
    },


        makePhoneCall:function(e){
                wx.makePhoneCall({
                        phoneNumber: e.currentTarget.dataset.tel
                });
        },


        
    // 页面数据加载
    changeStarff(e) {
        const self = this;
        //console.log("self.data.like "+self.data.like);
        var staff_id=e.currentTarget.dataset.id;
        app.util.request({
                url: 'Index/staffs',
                data: {
                        m: app.mdName,
                        aid:self.data.area.id,
                        cmd:'change',
                        staff_id:staff_id
                },
                success(res) {
                        var data = res.data.data;
                        self.setData({
                                my_staff:data.my_staff
                        });
                        
                        let pages = getCurrentPages();
                        if (pages.length >= 2) {
                                var prevPage = pages[pages.length - 2]; //上一个页面
                                //给pageA页面赋值
                                prevPage.setData({
                                        my_staff:data.my_staff
                                });
                        }
                }
        })
    },

})