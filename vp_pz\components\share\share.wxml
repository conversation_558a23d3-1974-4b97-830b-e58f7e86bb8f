<!--vp_xx/components/vp_xx/share.wxml--><!-- 弹窗 - 传播 -->
<view class="modal-mask" catchtouchmove="preventTouchMove" wx:if="{{shareModal}}"></view>
<view class="modal-dialog" wx:if="{{shareModal}}">
        <view class="vp-flex"  style="height:100%;flex-direction:column;">
                <view class="vp-flex_1" bindtap="hideShareModal"></view>
                <view>
                <view class="modal-main" style="background:#F4F4F4">
                        <view  class="modal-hd">
                                <text class="modal-hd-title">转发分享</text>
                                <image class="modal-hd-closer" src="../../resource/images/modal_closer.png" mode="widthFix" bindtap="hideShareModal"/>
                        </view>
                        <view  class="modal-bd" >
                                <view style="padding:60rpx 0;">
                                <view class="push-types">
                                        <view class="vp-flex">
                                        <view class="vp-flex_1">
                                                <button open-type="share" class="push-type">
                                                <view class="push-icon"><image src="../../resource/images/push_wx.png" mode="widthFix" /></view>
                                                <view class="push-text"><text>发给微信好友</text></view>
                                                </button>
                                        </view>
                                        <view class="vp-flex_1">
                                                <view class="push-type" bindtap="shareByPoster">
                                                <view class="push-icon"><image src="../../resource/images/push_img.png" mode="widthFix" /></view>
                                                <view class="push-text"><text>生成分享图片</text></view>
                                                </view>
                                        </view>
                                        </view>
                                </view>
                                </view>
                        </view>
                </view>
                </view>
        </view>
</view>
