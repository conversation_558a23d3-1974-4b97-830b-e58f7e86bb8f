<!--vp_timer/pages/index/index.wxml-->
<!-- 一体化动态背景 - 页面打开时与内容一体 -->
<view class="integrated-background" wx:if="{{slides && slides.length > 0}}">
    <view class="background-overlay"></view>
    <image class="background-image {{currentSlideIndex % 2 === 0 ? 'slide-effect-1' : 'slide-effect-2'}}" src="{{slides[currentSlideIndex].pic_image_url}}" mode="aspectFill"></image>
</view>

<navBar title-text="" isNav="{{false}}" isHeight="{{true}}"  background="none" bind:navBarAttached="onNavBarAttached"  />

<!-- 滚动时的固定遮挡头部 -->
<view class="fixed-header-mask" style="position:fixed;top:0;left:0;right:0;z-index:10;opacity:{{headerMaskOpacity}};pointer-events:{{headerMaskOpacity > 0 ? 'auto' : 'none'}}">
        <!-- 静态背景快照 - 只覆盖头部区域 -->
        <view class="static-background-snapshot" style="position:absolute;top:0;left:0;right:0;height:{{statusHeight + navHeight}}px;overflow:hidden;">
                <image class="snapshot-image" src="{{lockedBackgroundImage}}" mode="aspectFill" style="position:absolute;top:0;left:0;width:100%;height:750rpx;transform:scale(1.3);filter:blur(20px) brightness(0.85);"></image>
                <view class="snapshot-overlay" style="position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(255,255,255,0.3);"></view>
                <!-- 底部渐变边缘，让遮挡更自然 -->
                <view class="mask-gradient-edge" style="position:absolute;bottom:-10px;left:0;right:0;height:20px;background:linear-gradient(to bottom, rgba(255,255,255,0.3), transparent);"></view>
        </view>

        <!-- 头部内容 -->
        <view class="header-content" style="position:relative;z-index:1;">
                <view class="headStatus" style="height:{{statusHeight}}px;"></view>
                <view class="headNav" style="height:{{navHeight}}px;line-height:{{navHeight}}px;padding-left:20rpx;">
                        <view class="vp-flex">
                                <view>
                                    <view style="display:flex;align-items:center;">
                                        <image src="../../resource/images/ic_city.png" style="width:30rpx;height:30rpx;margin-right:5rpx;" mode="aspectFill"></image>
                                        <text style="color:#FFFFFF;font-size:30rpx;font-weight:bold;" bindtap="toAreas">{{area.name}}</text>
                                    </view>
                                </view>
                                <view class="vp-flex_1" style="overflow:hidden;">
                                        <navigator url="../index/search" style="height:{{menu.height}}px;line-height:{{menu.height}}px;margin-top:{{menu.top-statusHeight}}px;margin-left:16px;margin-right:{{menu.width+11}}px;background:#F4F4F4;border-radius:200rpx;text-align:center;">
                                                <text class="search-text">找医院</text>
                                        </navigator>
                                </view>
                        </view>
                </view>
        </view>
</view>

<!-- 原始透明头部 - 页面打开时显示 -->
<view class="original-header" style="position:absolute;top:0;left:0;right:0;z-index:2;opacity:{{1-headerMaskOpacity}}">
        <view class="headStatus" style="height:{{statusHeight}}px;"></view>
        <view class="headNav" style="height:{{navHeight}}px;line-height:{{navHeight}}px;padding-left:20rpx;">
                <view class="vp-flex">
                        <view>
                            <view style="display:flex;align-items:center;">
                                <image src="../../resource/images/ic_city.png" style="width:30rpx;height:30rpx;margin-right:5rpx;" mode="aspectFill"></image>
                                <text style="color:#FFFFFF;font-size:30rpx;font-weight:bold;" bindtap="toAreas">{{area.name}}</text>
                            </view>
                        </view>
                        <view class="vp-flex_1" style="overflow:hidden;">
                                <navigator url="../index/search" style="height:{{menu.height}}px;line-height:{{menu.height}}px;margin-top:{{menu.top-statusHeight}}px;margin-left:16px;margin-right:{{menu.width+11}}px;background:#F4F4F4;border-radius:200rpx;text-align:center;">
                                        <text class="search-text">找医院</text>
                                </navigator>
                        </view>
                </view>
        </view>
</view>

<!--主显示区-->
<block wx:if="{{main_loaded}}">
        <block wx:if="{{addmy}}">
                <view  class="weui-cell" style="background:#fff9eb;">
                        <view class="weui-cell__hd" >
                                <image src="../../resource/images/ic_myapp.png" style="display:block;width:40rpx;height:40rpx;margin-right:15rpx;" mode="widthFix"/>
                        </view>
                        <view class="weui-cell__bd" >
                        <text style="color:#be9719;font-size:26rpx;">点击右上角“添加到我的小程序”，方便下次找到！</text>
                        </view>
                        <view class="weui-cell__ft">
                                <image bindtap="closeAddmy" src="../../resource/images/modal_closer.png" style="display:block;width:30rpx;height:30rpx;" mode="widthFix"/>
                        </view>
                </view>
        </block>

        <block wx:if="{{area.staff_card>0}}">
                <block wx:if="{{my_staff}}">
                        <view style="position:sticky;top:{{navBarHeight}}px;z-index:2;background:#FFFFFF;opacity: 0.9;">
                                <view class="weui-cell staff" style="padding:15rpx 20rpx;">
                                        <view class="weui-cell__hd">
                                                <view class="st-avatar">
                                                        <image src="{{my_staff.avatar_url}}" mode="aspectFill" style="display:block;width:90rpx;height:90rpx;border-radius:200rpx;margin-right:15rpx;"/>
                                                </view>
                                        </view>
                                        <view class="weui-cell__bd">
                                                <view><text class="st-name" style="font-weight:bold;;">{{my_staff.nickname}}</text> <navigator url="staffs" class="f4" style="display:inline;margin-left:15rpx;color:#0bb585">[更换]</navigator></view>
                                                <view style="margin-top:10rpx;opacity: 0.7;font-size:22rpx;">我是您服务专员，欢迎随时咨询</view>
                                        </view>
                                        <view class="weui-cell__ft">
                                                <button class="btn1m btn1m-plain" bindtap="makePhoneCall" data-tel="{{my_staff.mobile}}">咨询</button>
                                        </view>
                                </view>
                        </view>
                </block>
                <block wx:else>
                        <view style="position:sticky;top:{{navBarHeight}}px;z-index:2;background:#FFFFFF;opacity: 0.9;">
                                <navigator url="staffs" class="weui-cell staff" style="padding:15rpx 20rpx;">
                                        <view class="weui-cell__hd">
                                                <view class="st-avatar">
                                                        <image src="../../resource/images/staff_def.png" mode="aspectFill" style="display:block;width:40rpx;height:40rpx;margin-right:15rpx;"/>
                                                </view>
                                        </view>
                                        <view class="weui-cell__bd">
                                                <view><text style="font-size:26rpx;font-weight:bold;">自动为您安排陪护师</text></view>
                                        </view>
                                        <view class="weui-cell__ft weui-cell__ft_in-access">
                                                <text class="f4" style="color:#0bb585;">我要指定</text>
                                        </view>
                                </navigator>
                        </view>
                </block>
        </block>
        


        <view wx:if="{{slides && slides.length>0}}" class="index-swiper" >
                <swiper  autoplay="{{true}}" circular="{{true}}"  interval="{{4000}}" duration="{{500}}" bindchange="onSwiperChange" >
                        <block wx:for="{{slides}}" >
                                <swiper-item>
                                        <image src="{{item.pic_image_url}}" mode="widthFix" show-menu-by-longpress bindtap="onSlideTap" data-index="{{index}}" />
                                </swiper-item>
                        </block>
                </swiper>
        </view>

        <block wx:if="{{nav2s && nav2s.length>0}}">
                <view class="nav2-list">
                        <block wx:for="{{nav2s}}" >
                                <view class="nav2-item" bindtap="onNav2Tap" data-index="{{index}}">
                                        <view class="nav2-pic"><image src="{{item.pic_image_url}}" mode="widthFix"/></view>
                                </view>
                        </block>
                </view>
        </block>

        <block wx:if="{{navs && navs.length>0}}">
                <view class="nav-list">
                        <block wx:for="{{navs}}" >
                                <view class="nav-item" bindtap="onNavTap" data-index="{{index}}">
                                        <view class="nav-pic"><image src="{{item.pic_image_url}}" mode="aspectFill"/></view>
                                        <view class="nav-text" style="color:{{item.tcolor?item.tcolor:''}}">{{item.title}}</view>
                                </view>
                        </block>
                </view>
        </block>

        
        <view>
                <view class="weui-cells hosp-list">
                        <!--医院列表-->
                        <view wx:for="{{hospitals}}"  class="weui-cell  hosp-item weui-cell_access" hover-class="weui-cell_active" bindtap="toHospital" data-hid="{{item.id}}">
                                <view class="weui-cell__hd">
                                        <image class="hosp-avatar" src="{{item.avatar?item.avatar_url:'../../resource/images/avatar.jpg'}}" mode="aspectFill"  />
                                </view>
                                <view class="weui-cell__bd">
                                        <view>
                                                <text class="hosp-name">{{item.name}}</text>
                                        </view>
                                        <view class="hosp-line">
                                                <text class="hosp-rank">{{item.rank}}</text> <text class="hosp-label">{{item.label}}</text>
                                        </view>
                                        <view class="hosp-line" style="display:flex;align-items:center;">
                                                <image style="margin-right:5px;display:block;width:28rpx;height:28rpx;" mode="aspectFill" src="../../resource/images/ic_address.png" role="img"></image>
                                                <text class="hosp-intro">{{item.city || ''}}{{item.district || ''}}{{item.address || '暂无地址信息'}}</text>
                                        </view>
                                </view>
                        </view>
                </view>
        </view>

        <view hidden="{{!is_loading_more}}" style="padding:40rpx;text-align:center;" >
                <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
        </view>

        <navigator  url="../index/server" class="vp-server">
                <view><image class="vp-server-icon" src="../../resource/images/ic_server.png" mode="widthFix" /></view>
                <view><text class="vp-server-text" >客服</text></view>
        </navigator>

        <footer cfg="{{cfg}}" />

        <vpShare shareModal="{{_shareModal}}"></vpShare>
 </block>

<block wx:else>
        <view style="padding:200rpx 0 0 0;text-align:center;" >
                <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
        </view>
</block>   

