<!--vp_cai/pages/index/citys.wxml-->
<!--vp_timer/pages/index/user.wxml-->
<navBar title-text="" isHeight="{{false}}" isWhite="{{true}}" background="none" bind:navBarAttached="onNavBarAttached"/>
<block wx:if="{{main_loaded}}">
        <view style="position:relative;">
                <view>
                        <image src="{{hospital.avatar_url}}" mode="aspectFill"  style="filter: blur(50rpx) brightness(0.8);display:block;width:100%;height:550rpx;overflow:hidden"></image>
                </view>
                <view style="position:absolute;top:{{navBarHeight}}px;padding-top:65rpx;overflow:hidden;width:100%;">
                        <view class="hospital-hd">
                                <view class="weui-cell  weui-cell_access" hover-class="weui-cell_active" bindtap="showShareModal">
                                        <view class="weui-cell__hd">
                                                <image src="{{hospital.avatar_url}}" mode="aspectFill"  style="position:absolute;top:-65rpx;display:block;width:150rpx;height:135rpx;border-radius:10rpx;overflow:hidden"></image>
                                        </view>
                                        <view class="weui-cell__bd" style="padding-left:170rpx;">
                                                <view style="position:absolute;top:-65rpx;"><text style="font-size:36rpx;color:#FFFFFF;font-weight:bold;">{{hospital.name}}</text></view>
                                                <!--<view style="width:450rpx;overflow:hidden;white-space: nowrap;text-overflow:ellipsis;"><text>{{hospital.intro}}</text></view>-->
                                                <view><text class="hosp-rank">{{hospital.rank}}</text> <text class="hosp-label">{{hospital.label}}</text></view>
                                        </view>
                                        
                                        <view class="weui-cell__ft weui-cell__ft_in-access"><text class="f4">转发</text></view>
                                </view>
                                <view class="weui-cell  weui-cell_access" hover-class="weui-cell_active" bindtap="toMap" >
                                        <view class="weui-cell__hd">
                                                <image src="../../resource/images/ic_address.png" mode="aspectFill"  style="margin-right:10rpx;display:block;width:40rpx;height:40rpx;"></image>
                                        </view>
                                        <view class="weui-cell__bd">
                                                <view><text style="font-size:24rpx;">{{hospital.city}}{{hospital.district}}{{hospital.address}}</text></view>
                                        </view>
                                        <view class="weui-cell__ft weui-cell__ft_in-access"><text class="f4">导航</text></view>
                                </view>
                        </view>

                        <view class="hospital-bd">
                                
                                <view class="weui-cells serv-list">
                                        <view class="weui-cell serv-item" >
                                                <view class="weui-cell__bd">
                                                        <view style="padding-top:10rpx;"><text class="serv-name">在线预约您需要的服务</text></view>
                                                </view>
                                                <view class="weui-cell__ft ">
                                                       
                                                </view>
                                        </view>
                                        <view wx:for="{{services}}" wx:if="{{item.use_switch==1}}" class="weui-cell serv-item" bindtap="toService" data-svid="{{item.id}}" >
                                                <view class="weui-cell__hd">
                                                        <image class="serv-logo" src="{{item.logo_image?item.logo_image_url:'../../resource/images/avatar.jpg'}}" mode="aspectFill"  />
                                                </view>
                                                <view class="weui-cell__bd">
                                                        <view><text class="serv-name">{{item.name}}</text></view>
                                                        <view class="serv-line serv-intro">{{item.intro}}</view>
                                                        <view class="serv-line"><text class="serv-price">{{item.price}}</text> <text class="serv-unit">元/次</text></view>
                                                </view>
                                                <view class="weui-cell__ft ">
                                                        <button class="btn1m">预约</button>
                                                </view>
                                        </view>
                                </view>

                        </view>
                        
                        <!--
                        <view class="hospital-ft">
                                <navigator class="weui-cell weui-cell_access" url="../index/server" >
                                        <view class="weui-cell__hd">
                                                <view><image src="../../resource/images/ic_server.png" mode="widthFix"  style="display:block;width:40rpx;height:40rpx;margin-right:15rpx;"/></view>
                                                
                                        </view>
                                        <view class="weui-cell__bd">
                                                <view>咨询客服</view>
                                        </view>
                                        <view class="weui-cell__ft weui-cell__ft_in-access">
                                        </view>
                                </navigator>
                        </view>
                        -->

                        <footer cfg="{{cfg}}" />

                       
                </view>
        </view>

   
      

        <vpShare shareModal="{{_shareModal}}"></vpShare>

 </block>

<block wx:else>
        <view style="padding:200rpx 0 0 0;text-align:center;" >
                <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
        </view>
</block>   



