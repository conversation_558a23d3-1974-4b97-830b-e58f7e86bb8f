<!--vp_timer/pages/index/user.wxml-->
<block wx:if="{{main_loaded}}">
        
        <view class="st-banner">
                <view class="weui-cells"  style="margin:0;background:#ec752c;" >
                        <view class="weui-cell">
                                <view class="weui-cell__hd">
                                        <view class="st-avatar">
                                                <block wx:if="{{mine.avatar}}">
                                                        <image src="{{mine.avatar_url}}" mode="aspectFit"/>
                                                </block>
                                                <block wx:else>
                                                        <image src="../../resource/images/avatar.jpg" mode="aspectFit"/>
                                                </block>
                                        </view>
                                </view>
                                <view class="weui-cell__bd">
                                        <view><text class="st-name">{{mine.nickname?mine.nickname:('用户'+mine._id)}}</text></view>
                                        <view style="margin-top:10rpx;opacity: 0.7;"><navigator class="st-mobile" url="mobile">{{mine.mobile?mine.mobile:'未填写手机号'}}</navigator></view>
                                </view>
                                <view class="weui-cell__ft">
                                        <!--<image src="../../resource/images/ic_settings.png"  style="width:40rpx;height:40rpx;"/>-->
                                </view>
                        </view>

                        <!--
总单数，总收入，余额，已提现
【推广】【提现】
-->
                        <!--
                        <navigator class="weui-cell func" url="money">
                                <view class="weui-cell__hd"><image src="../../resource/images/ic_money.png" /></view>
                                <view class="weui-cell__bd">
                                        <view>余额</view>
                                </view>
                                <view class="weui-cell__ft weui-cell__ft_in-access">{{staff.money}}元</view>
                        </navigator>
                        <navigator wx:if="{{area.staff_team==2}}" class="weui-cell func"  url="team">
                                <view class="weui-cell__hd"><image src="../../resource/images/ic_team.png" /></view>
                                <view class="weui-cell__bd">团队</view>
                                <view class="weui-cell__ft weui-cell__ft_in-access">{{staff.subs}}人</view>
                        </navigator>
                         -->
                </view>
                <navigator url="{{cfg.page_seller}}" style="display:inline-block;padding:15rpx 20rpx 15rpx 25rpx;border-radius:50rpx 0 0 50rpx;background:#FFFFFF;font-size:26rpx;font-weight:bold;color:#ec752c;position:absolute;right:0;top:35rpx;">
                        推广说明
                </navigator>
        </view>

        <!--
        <view  class="op-cells" style="background-color: #FFFFFF;padding:0 0 60rpx 0;overflow:hidden;text-align:center">
                <view style="margin-top:40rpx;">

                </view>
                <view style="padding-top:20rpx;">
                        
                        <block wx:if="{{mine.nickname}}">
                                <text style="font-weight:bold;margin:0 5rpx 0 50rpx;" bindtap="toAuth">{{mine.nickname}}</text> 
                                <text class="color_click" bindtap="updateUserInfo">更新</text>
                                
                        </block>
                        <block wx:else>
                                <button class="vp-button color_click" open-type="getUserInfo" bindgetuserinfo="updateUserInfo">一键授权微信登录</button>
                        </block>
                </view>
        </view>
        -->
        <!--
        <view class="weui-cells op-cells" style="margin-top:20rpx;" >  
                <navigator class="weui-cell" url="money">
                        <view class="weui-cell__bd">
                                <view>余额：{{staff.money}}元</view>
                        </view>
                        <view class="weui-cell__ft"></view>
                </navigator>
                <navigator wx:if="{{city.staff_team==2}}" class="weui-cell weui-cell_access"  hover-class="weui-cell_active" url="team">
                        <view class="weui-cell__bd">团队</view>
                        <view class="weui-cell__ft weui-cell__ft_in-access"></view>
                </navigator>
        </view>
        -->

        <view style="position:relative;">
                <view style="position:absolute;top:-50rpx;padding-bottom:40rpx;left:0;width:100%;">
                        <view class="weui-cells op-cells" style="margin-top:20rpx;">  
                                <view class="weui-cell">
                                        <view class="weui-cell__bd">
                                                <view style="font-size:48rpx;font-weight:bold">{{mine.sell_money}}</view>
                                                <view style="font-size:22rpx;color:#999999;">我的账户</view>
                                        </view>
                                        <view class="weui-cell__ft">
                                                <button class="btn1m" style="background:#ec752c !important;border-color:#ec752c !important;margin-right:10rpx;" bindtap="showShareModal">推广</button>
                                                <button class="btn1m" bindtap="toOutcash">提现</button>
                                        </view>
                                </view>
                                <view class="weui-cell" style="padding:0">
                                        <view class="weui-cell__bd">
                                                <view class="data-cell">
                                                        <view class="data-val">{{area.tax_seller}}%</view>
                                                        <view class="data-txt">返佣比例</view>
                                                </view>
                                        </view>

                                        <view class="weui-cell__bd">
                                                <view class="data-cell">
                                                        <view class="data-val">{{mine.sells}}</view>
                                                        <view class="data-txt">推广有效单</view>
                                                </view>
                                        </view>
                                        
                                        <view class="weui-cell__bd">
                                                <view class="data-cell">
                                                        <view class="data-val">{{mine.sell_income}}</view>
                                                        <view class="data-txt">总佣金收入</view>
                                                </view>
                                        </view>
                                        <view class="weui-cell__bd">
                                                <view class="data-cell" >
                                                        <view class="data-val">{{mine.sell_outcash}}</view>
                                                        <view class="data-txt">已提现</view>
                                                </view>
                                        </view>
                                </view>
                        </view>



                        <view class="vp-list-tt" style="margin-top:20rpx;"><text>- 我的账户记录 -</text></view>

                        <view wx:if="{{list!=null && list.length==0}}"  style="padding:40rpx 40rpx 40rpx 40rpx;text-align:center;">
                                <image src="../../resource/images/empty.png" mode="widthFix" style="width:300rpx;"/>
                                <view class="f5">还没有收支记录~</view>
                        </view>
                        <view wx:else >
                                <view class="weui-cells"  style="margin-top:10rpx;">
                                        <view wx:for="{{list}}" class="weui-cell weui-cell_access">
                                                <view class="weui-cell__bd" >
                                                        <view style="font-weight:bold;color:{{item.money>0?'#09bb07':'#e64340'}}">{{item.money>0?('+'+item.money):item.money}}元</view>
                                                </view>
                                                <view class="weui-cell__ft">
                                                        <view class="f5">{{item.biz_name}}</view>
                                                        <view class="f6"><formater timestamp="{{item.createtime}}" format="YYYY-MM-dd hh:mm:ss"></formater></view>
                                                </view>
                                        </view>
                                </view>
                                <view hidden="{{!is_loading_more}}" style="padding:40rpx;text-align:center;" >
                                <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
                                </view>
                                <view hidden="{{more==1}}" class="f5" style="padding:40rpx;text-align:center;">
                                - 没有更多记录了 -
                                </view>
                        </view> 


                </view>
        </view>

 </block>

<block wx:else>
        <view style="padding:200rpx 0 0 0;text-align:center;" >
                <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
        </view>
</block>   

<vpShare shareModal="{{_shareModal}}"></vpShare>




    <!-- 弹窗 - 提现 -->
    <view class="modal-mask" catchtouchmove="preventTouchMove" wx:if="{{_regModal}}"></view>
    <view class="modal-dialog" wx:if="{{_regModal}}">
            <view class="vp-flex"  style="height:100%;flex-direction:column;">
                    <view class="vp-flex_1" >
                    </view>
                    <view >
                           <view class="modal-main" >
                                <view  class="modal-hd">
                                        <text class="modal-hd-title">成为推广者</text>
                                </view>
                                <view  class="modal-bd" >
                                        <view style="margin:0 40rpx 40rpx 40rpx;">

                                                <view style="background:#fff6f0">
                                                        <navigator url="{{cfg.page_seller}}" class="weui-cell  weui-cell_access" style="border:1rpx solid #ec752c;border-radius:10rpx;">
                                                                <view class="weui-cell__bd">
                                                                        <view style="color:#ec752c;font-size:26rpx;">成为推广者可赚取服务订单佣金</view>
                                                                        <view style="color:#ec752c;font-size:26rpx;">具体规则请先点此阅读《推广说明》</view>
                                                                </view>
                                                                <view class="weui-cell__ft weui-cell__ft_in-access"></view>
                                                        </navigator>
                                                </view>


                                                <view style="text-align:center;margin-top:30rpx;">
                                                        <block wx:if="{{area.seller_reg==2}}">
                                                                <button class="btnp" style="background:#ec752c !important;border-color:#ec752c !important;"  bindtap="toServer">联系客服开通</button>
                                                        </block>
                                                        <block wx:if="{{area.seller_reg==3}}">
                                                                <button class="btnp" style="background:#ec752c !important;border-color:#ec752c !important;"  bindtap="toSeller">我已了解，立即成为推广者</button>
                                                        </block>
                                                </view>
                                        </view>
                                </view>
        
                           </view>
                    </view>
            </view>
    </view>