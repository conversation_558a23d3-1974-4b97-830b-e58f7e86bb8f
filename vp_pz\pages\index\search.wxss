/* vp_crm/pages/shopm/good_list.wxss */
page{background:#FFFFFF;}


.hosp-list{margin:10rpx 0 0 0 ;background:none;}
.hosp-list::before{display:none;}
.hosp-list::after{display:none;}

.hosp-item{-webkit-box-align:center;-webkit-align-items:center;align-items:center;padding:20rpx;margin:20rpx;border-radius:10rpx;overflow:hidden;box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.04), 0 1px 6px 0 rgba(0, 0, 0, 0.04);}
.hosp-item::before{display:none;}
.hosp-item::after{display:none;}
.hosp-name{font-weight:bold;font-size:34rpx;}
.hosp-avatar{display:block;width:80rpx;height:80rpx;border-radius:10rpx;overflow:hidden;margin-right:20rpx;vertical-align:middle;}
.hosp-line{margin-top:5rpx;}
.hosp-line text{font-size:26rpx;}
.hosp-rank{font-weight:bold;color:#0bb585;margin-right:15rpx;}
.hosp-label{font-weight:bold;color:#0ca7ae;margin-right:15rpx;}
.hosp-intro{color:#999999;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis;}

/* 添加自定义样式，使内容垂直居中 */
.hosp-item .weui-cell__hd {
  display: flex;
  align-items: center;
}

.hosp-item .weui-cell__bd {
  display: flex;
  flex-direction: column;
  justify-content: center;
}