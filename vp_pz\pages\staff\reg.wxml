<!--vp_cai/pages/staff/reg.wxml-->
<block wx:if="{{main_loaded}}">

        <block wx:if="{{staff}}">
                <block wx:if="{{staff.status==10}}">
                        <view class="weui-cell" style="background:#10aeff">
                                <view class="weui-cell__hd">
                                </view>
                                <view class="weui-cell__bd">
                                        <view style="font-weight:bold;color:#FFFFFF">您的信息审核中，约需1~2个工作日</view>
                                </view>
                        </view>
                </block>
                <block wx:if="{{staff.status==5}}">
                        <view class="weui-cell"  style="background:#f76260">
                                <view class="weui-cell__hd">
                                </view>
                                <view class="weui-cell__bd">
                                        <view style="font-weight:bold;color:#FFFFFF">您的信息未通过审核，请修改后重新提交</view>
                                        <view style="font-weight:bold;color:#FFFFFF;font-size:22rpx;">原因：{{staff.status_remark}}</view>
                                </view>
                        </view>
                </block>
        </block>

    <!--1.授权获取头像和昵称-->
    <!--2.填写  姓名，头像，  性别，年龄，电话，    身份证名称，身份证号，身份证，    其他材料证件-->

    <form bindsubmit="staffSave" >
            <view>

                <block wx:if="{{area.staff_reg>1 && !(staff.id>0)}}">
                        <view class="pub-box">
                                <view class="pub-box-bd">
                                        <view class="weui-cell weui-cell_input" >
                                                <view class="weui-cell__hd">
                                                        邀请码
                                                </view>
                                                <view class="weui-cell__bd">
                                                        <input class="weui-input"  name="invite_code"  style="text-align:right" placeholder-class="vp-placeholder"  placeholder="{{area.staff_reg==3?'请向邀请人领取':'没有可不填'}}" value="" />
                                                </view>
                                        </view>
                                </view>
                        </view>
                </block>
                
                <view class="pub-box">
                        <view class="pub-box-bd">

                        <view class="weui-cell" >
                                <view class="weui-cell__hd">
                                        {{cfg.dict.avatar}}
                                </view>
                                <view class="weui-cell__bd"></view>
                                <view class="weui-cell__ft">
                                        <view class="logo-box" bindtap="onAvatarChange">
                                                <image  src="{{staff.avatar_file?staff.avatar_file:(staff.avatar?staff.avatar_url:'../../resource/images/pic_add.png')}}"  mode="aspectFit" />
                                        </view>
                                </view>
                        </view>
                        <view class="weui-cell weui-cell_input" >
                                <view class="weui-cell__hd">
                                        展示称呼
                                </view>
                                <view class="weui-cell__bd">
                                        <input class="weui-input"  name="nickname"  style="text-align:right" placeholder-class="vp-placeholder"  placeholder="设置您称呼用于服务展示" value="{{staff.nickname}}" />
                                </view>
                        </view>

                        <view class="weui-cell weui-cell_input" >
                                <view class="weui-cell__hd">
                                        性别
                                </view>
                                <view class="weui-cell__bd">
                                </view>
                                <view class="weui-cell__ft ">
                                        <text class="sex sex1 {{staff.sex==1?'sex1on':''}}" bindtap="onSexChange" data-sex="1">男</text><text class="sex sex2 {{staff.sex==2?'sex2on':''}}"  bindtap="onSexChange" data-sex="2">女</text>
                                </view>
                        </view>
                        <view class="weui-cell weui-cell_input" >
                                <view class="weui-cell__hd">
                                        年龄
                                </view>
                                <view class="weui-cell__bd">
                                        <input class="weui-input" type="number" name="age"  style="text-align:right" placeholder-class="vp-placeholder"  placeholder="请填写周岁年龄" value="{{staff.age}}" />
                                </view>
                        </view>
                        <view class="weui-cell weui-cell_input" >
                                <view class="weui-cell__hd">
                                        手机号
                                </view>
                                <view class="weui-cell__bd">
                                        <input class="weui-input" type="number" name="mobile"  style="text-align:right" placeholder-class="vp-placeholder"  placeholder="请填写您的手机号" value="{{staff.mobile}}" />
                                </view>
                        </view>
                        
                        <view wx:if="{{staff.status==10}}"  class="edit-disabled" bindtap="editDisabled" ></view>
                        </view>
                </view>



                <view class="pub-box">
                        <view class="pub-box-bd">
                                <view class="weui-cell weui-cell_input" >
                                        <view class="weui-cell__hd">
                                                {{cfg.dict.id_name}}
                                        </view>
                                        <view class="weui-cell__bd">
                                                <input class="weui-input" name="realname"  style="text-align:right" placeholder-class="vp-placeholder"  placeholder="请填写{{cfg.dict.id_name}}" value="{{staff.realname}}" />
                                        </view>
                                </view>
                                <view class="weui-cell weui-cell_input" >
                                        <view class="weui-cell__hd">
                                                {{cfg.dict.id_num}}
                                        </view>
                                        <view class="weui-cell__bd">
                                                <input class="weui-input" name="idcardnum"  style="text-align:right" placeholder-class="vp-placeholder"  placeholder="请填写{{cfg.dict.id_num}}" value="{{staff.idcardnum}}" />
                                        </view>
                                </view>
                                <view class="weui-cell" >
                                        <view class="weui-cell__hd">
                                                {{cfg.dict.id_pic}}
                                        </view>
                                        <view class="weui-cell__bd"></view>
                                        <view class="weui-cell__ft">
                                                <view class="logo-box" bindtap="onIdcard1Change">
                                                        <image  src="{{staff.idcard1_file?staff.idcard1_file:(staff.idcard1?staff._idcard1:'../../resource/images/pic_add.png')}}"  mode="aspectFit" />
                                                </view>
                                        </view>
                                        <view class="weui-cell__ft">
                                                <view class="logo-box" bindtap="onIdcard2Change">
                                                        <image  src="{{staff.idcard2_file?staff.idcard2_file:(staff.idcard2?staff._idcard2:'../../resource/images/pic_add.png')}}"  mode="aspectFit" />
                                                </view>
                                        </view>
                                </view>

                                <view wx:if="{{staff.status==10}}"  class="edit-disabled" bindtap="editDisabled" ></view>
                        </view>
                </view>


                    <view style="height:300rpx;"><!--底部占位--></view>



                <!-- 悬浮提交按钮-->
                <view class="vp-foot">
                        <view style="background:#FFFFFF;padding:20rpx;">
                                <block wx:if="{{staff.id>0}}">
                                        <block wx:if="{{staff.status==10}}">
                                               <!--审核期不给修改-->
                                               <button class="btnp btnp-disabled"  >正在审核中</button>
                                        </block>
                                        <block wx:if="{{staff.status==5}}">
                                                <button class="btnp"  formType="submit">保存并提交审核</button>
                                        </block>
                                </block>
                                <block wx:else>
                                        <!--新注册-->
                                        <view class="xieyi" style="text-align:center">
                                                <text class="is_xieyi {{is_xieyi?'is_xieyi_on':''}}" bindtap="onXieyiChange">我已阅读并同意</text><navigator url="{{cfg.page_xy}}">《用户协议》</navigator><text>和</text><navigator url="{{cfg.page_fw}}">《服务协议》</navigator>
                                        </view>
                                        <view>
                                                <button class="btnp {{is_xieyi?'':'btnp-disabled'}}"  formType="submit">注册成为陪护师</button>
                                        </view>
                                </block>
                        </view>
                </view>
            
            </view>
    </form>







    </block>
    <block wx:else>
    <view style="padding:200rpx 0 0 0;text-align:center;" >
            <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
    </view>
</block>


<!-- 弹窗 - 发布/保存成功 -->
<view class="modal-mask" catchtouchmove="preventTouchMove" wx:if="{{_savedModal}}"></view>
<view class="modal-dialog" wx:if="{{_savedModal}}">
        <view class="vp-flex"  style="height:100%;flex-direction:column;">
                <view class="vp-flex_1" ></view>
                <view>
                        <view class="modal-main" style="background:#FFFFFF;">
                                <view  class="modal-bd">
                                        <view style="text-align:center;padding:60rpx 30rpx 40rpx 30rpx;">
                                                <view> <icon  type="waiting" size="60"></icon></view>
                                                <view class="f3" style="margin-top:20rpx;font-weight:bold;">发布成功，审核后即可转发和展示</view>
                                                <view><text class="f5">审核一般不超过2小时，审核期间可修改</text></view>
                                                <view style="margin-top:40rpx;"><button class="btnp" bindtap="liveSaved">确定并预览</button></view>
                                        </view>
                                </view>
                        </view>
                </view>
        </view>
</view>
