
<block wx:if="{{main_loaded}}">

    <form bindsubmit="profileSave" >

        <view class="pub-box">
                <view class="pub-box-bd">

                    <view class="weui-cell" >
                            <view class="weui-cell__hd">
                                    头像
                            </view>
                            <view class="weui-cell__bd"></view>
                            <view class="weui-cell__ft">
                                    <view class="logo-box" bindtap="onAvatarChange">
                                            <image  src="{{avatar_file?avatar_file:(mine.avatar?mine.avatar_url:'../../resource/images/avatar.jpg')}}"  mode="aspectFit" />
                                    </view>
                            </view>
                    </view>
                    <view class="weui-cell weui-cell_input" >
                            <view class="weui-cell__hd">
                                    昵称
                            </view>
                            <view class="weui-cell__bd">
                                    <input class="weui-input"  name="nickname"  style="text-align:right" placeholder-class="vp-placeholder"  placeholder="如何称呼您" value="{{mine.nickname}}"  bindinput="onNicknameChange"/>
                            </view>
                    </view>
                </view>
        </view>

    
        <!-- 悬浮提交按钮-->
        <view class="vp-foot">
                <view style="background:#FFFFFF;padding:20rpx;">
                        <!--新注册
                        <view class="xieyi" style="text-align:center">
                                <text class="is_xieyi {{is_xieyi?'is_xieyi_on':''}}" bindtap="onXieyiChange">我已阅读并同意</text><navigator>《用户协议》</navigator><text>和</text><navigator>《服务协议》</navigator>
                        </view>
                        -->
                        <view>
                            <block wx:if="{{is_changed}}">
                                    <button class="btnp"  formType="submit">保存修改</button>
                            </block>
                            <block wx:else>
                                    <button class="btnp btnp-disabled" >修改后保存</button>
                            </block>
                        </view>
                </view>
        </view>
        

    </form>


</block>
<block wx:else>
    <view style="padding:200rpx 0 0 0;text-align:center;" >
        <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
    </view>
</block>

