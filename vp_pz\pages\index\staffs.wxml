<!--vp_cai/pages/index/citys.wxml-->

<view class="weui-cells" style="margin:0">
        <view class="weui-cell">
                <view class="weui-cell__hd">
                        <image src="../../resource/images/staff_def.png" mode="aspectFill" style="display:block;width:80rpx;height:80rpx;margin-right:20rpx;"/>
                </view>
                <view class="weui-cell__bd">
                        <view style="font-weight:bold;color:#0bb585;">自动安排</view>
                        <view class="infos">
                                <text>根据服务项目自动安排合适的人员</text>
                        </view>
                </view>
                <div class="weui-cell__ft">
                        <block wx:if="{{!my_staff}}"><button class="btn1m btn1m-disabled">当前</button></block>
                        <block wx:else><button class="btn1m" bindtap="changeStarff" data-nickname="自动安排" data-id="0">选择</button></block>
                </div>
        </view>
        <block wx:for="{{staffs}}">
                <view class="weui-cell">
                        <view class="weui-cell__hd">
                                <image src="{{item.avatar_url}}" mode="aspectFill" style="display:block;width:80rpx;height:80rpx;border-radius:100rpx;margin-right:20rpx;"/>
                        </view>
                        <view class="weui-cell__bd">
                                <view style="font-weight:bold;color:#0bb585;">{{item.nickname}}</view>
                                <view class="infos">
                                        <text class="sext{{item.sex}}">{{item.sex==1?'男':'女'}}</text> <text style="margin-left:10rpx">{{item.age}}岁</text> <text class="color_click" style="margin-left:10rpx" bindtap="makePhoneCall" data-tel="{{item.mobile}}">电话咨询</text>
                                </view>
                        </view>
                        <div class="weui-cell__ft">
                                <block wx:if="{{my_staff && my_staff.id==item.id}}"><button class="btn1m btn1m-disabled">当前</button></block>
                                <block wx:else><button class="btn1m" bindtap="changeStarff" data-nickname="{{item.nickname}}" data-id="{{item.id}}">指定</button></block>
                        </div>
                </view>
        </block>
</view>