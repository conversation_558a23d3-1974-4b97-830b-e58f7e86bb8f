<block wx:if="{{main_loaded}}">
        <view style="width:100%;border-bottom:0 none;position:fixed;z-index:2">
                <view style="background:#FFFFFF;position:relative;">
                        <view class="h-tab vp-flex">
                                <view class="h-tab-item vp-flex_1 {{filt==0? 'on' : ''}}" data-filt="0"  bindtap="onFilterChange">全部</view>
                                <view class="h-tab-item vp-flex_1 {{filt==10 ? 'on' : ''}}" data-filt="10"  bindtap="onFilterChange">待支付</view>
                                <view class="h-tab-item vp-flex_1 {{filt==20 ? 'on' : ''}}" data-filt="20"  bindtap="onFilterChange">待服务</view>
                                <view class="h-tab-item vp-flex_1 {{filt==30 ? 'on' : ''}}" data-filt="30"  bindtap="onFilterChange">已完成</view>
                                <view class="h-tab-item vp-flex_1 {{filt==40 ? 'on' : ''}}" data-filt="40"  bindtap="onFilterChange">已取消</view>
                        </view>
                </view>
        </view>
        <view style="height:100rpx"></view>

        <block wx:if="{{list!=null}}">
                <view wx:if="{{list!=null && list.length==0}}"  style="padding:40rpx 40rpx 40rpx 40rpx;text-align:center;">
                        <image src="../../resource/images/empty.png" mode="widthFix" style="width:200rpx;"/>
                        <view class="f5" >没有相关内容~</view>
                </view>

                <view wx:else  class="od-list">
                        <block wx:for="{{list}}">
                                <view class="od-item" bindtap="toOrder" data-id="{{item.id}}">
                                        <view class="weui-cell weui-cell_access" >
                                                <view class="weui-cell__hd" >
                                                        <view>
                                                                <image src="{{item.service_logo_image_url}}" mode="widthFix" class="od-logo" style="width:100rpx;height:100rpx;margin-right:20rpx;"/>
                                                        </view>
                                                </view>
                                                <view class="weui-cell__bd" >
                                                        <view>
                                                                <view><text style="font-weight:bold">{{item.service_name}}</text></view>

                                                                <view class="od-info">
                                                                        <block wx:if="{{item.service_stype < 200}}">
                                                                                <block wx:if="{{item.service_stype <= 20}}">
                                                                                        <view><text>{{item.hospital_name}}（{{item.area_name}}）</text></view>
                                                                                        <view>预约时间：<formater  timestamp="{{item.starttime}}"  format="MM-dd hh:mm"></formater></view>
                                                                                        <view>就诊人员：<text>{{item.client_name}}</text></view>
                                                                                </block>
                                                                                <block wx:if="{{item.service_stype > 20 && item.service_stype < 100}}">
                                                                                        <view><text>{{item.hospital_name}}（{{item.area_name}}）</text></view>
                                                                                        <view>处理时间：<formater  timestamp="{{item.starttime}}"  format="MM-dd hh:mm"></formater></view>
                                                                                </block>
                                                                                <block wx:if="{{item.service_stype > 100}}">
                                                                                        <view>服务时间：<formater  timestamp="{{item.starttime}}"  format="MM-dd hh:mm"></formater></view>
                                                                                        <view>服务对象：<text>{{item.client_name}}</text></view>
                                                                                        <!--<view>服务地址：<text>{{item.address.address}}</text> </view>-->
                                                                                </block>
                                                                        </block>
                                                                        <block else>
                                                                                <view>支付费用：<text>{{item.price}}</text>元</view>
                                                                        </block>
                                                                </view>

                                                        </view>
                                                </view>
                                                <view class="weui-cell__ft">
                                                        <!--待付款-->
                                                        <block wx:if="{{item.status==10}}">
                                                                <block wx:if="{{item._exp_time>0}}">
                                                                        <view style="color:#ffa200"><text>待支付</text></view>
                                                                        <view style="color:#ffa200"><counter style="font-size:24rpx;" second="{{item._exp_time*1000}}" bind:counterOver="onCounterOver" /></view>
                                                                        
                                                                </block>
                                                                <block wx:else>
                                                                        <view style="color:#999999"><text>支付超时</text></view>
                                                                        <view style="color:#999999"><text>已取消</text></view>
                                                                </block>
                                                        </block>
                                                        <!--进行中-->
                                                        <block wx:if="{{item.status==20}}">
                                                                <view style="color:#1da6fd"><text>待服务</text></view>
                                                        </block>
                                                        <!--已完成-->
                                                        <block wx:if="{{item.status==30}}">
                                                                <view style="color:#21c521"><text>已完成</text></view>
                                                        </block>
                                                        <!--已取消-->
                                                        <block wx:if="{{item.status==40}}">
                                                                <view style="color:#999999"><text>已取消</text></view>
                                                        </block>
                                                </view>
                                        </view>
                                </view>
                        </block>
                        <view hidden="{{!is_loading_more}}" style="padding:40rpx;text-align:center;" >
                                <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
                        </view>
                        <view hidden="{{more==1}}" class="f5" style="padding:40rpx;text-align:center;">
                                - 嗯，目前就这么多 -
                        </view>
                </view>
        </block>
        <block wx:else>
                <view style="padding:200rpx 0 0 0;text-align:center;" >
                        <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
                </view>
        </block>   
</block>

<block wx:else>
        <view style="padding:200rpx 0 0 0;text-align:center;" >
                <image src="../../resource/images/loading.gif" mode="widthFix" style="width:100rpx;height:100rpx;"/>
        </view>
</block>   



