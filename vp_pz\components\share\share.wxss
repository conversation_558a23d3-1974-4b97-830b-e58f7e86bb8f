/* vp_xx/components/vp_xx/share.wxss */
.vp-btn-share{background:none !important;border:0 none !important;border-radius:0;padding:0 !important;line-height:1em;margin:0;padding:0;position:fixed;display:block;width:80rpx;height:80rpx;border-radius:100rpx;right:20rpx;bottom:210rpx;margin-bottom:env(safe-area-inset-bottom);}
.vp-btn-share::after{border:0 none;}
.vp-btn-share image{width:100%;height:100%;}


/** VP_SC  modal **/
.modal-mask {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: #000;
    opacity: 0.5;
    overflow: hidden;
    z-index: 9000;
    color: #fff;
  }
  
  .modal-dialog {
    width: 100%;
    height:100%;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  /**  background: #10aeff;
    border-radius: 36rpx 36rpx 0 0;**/
    /** border:1rpx solid #FFFFFF; box-shadow:-20rpx 0 60rpx 10rpx #EEEEEE;**/
  }
  
  .modal-title {
    padding-top: 50rpx;
    font-size: 36rpx;
    color: #030303;
    text-align: center;
  }
  
  .modal-content {
    padding: 50rpx 32rpx;
  }
  
  .modal-input {
    display: flex;
    background: #fff;
    border: 2rpx solid #ddd;
    border-radius: 4rpx;
    font-size: 28rpx;
  }
  .modal-footer {
    display: flex;
    flex-direction: row;
    height: 86rpx;
    border-top: 1px solid #dedede;
    font-size: 34rpx;
    line-height: 86rpx;
  }
  
  .modal-main{background:#FFFFFF;border-radius:20rpx 20rpx 0 0;overflow:hidden;height:100%;width:100%}
  .modal-hd{padding:40rpx 0;text-align:center;position:relative}
  .modal-hd-title{font-weight:bold;font-size:28rpx;}
  .modal-hd-closer{width:20px;height:20rpx;position:absolute;left:30rpx;top:40rpx}
  .modal-hd-button{position:absolute;right:30rpx;top:30rpx}
  .modal-bd{padding-bottom: env(safe-area-inset-bottom);}
  
  /** VP_SC  modal **/


  
.vp-flex{
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
  .vp-flex_1{
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-tap-highlight-color: transparent;
  }
  .vp-flex_2{
    -webkit-box-flex: 2;
    -webkit-flex: 2;
    -ms-flex: 2;
    flex: 2;
    -webkit-tap-highlight-color: transparent;
  }
  .vp-flex-center{position:relative;display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;align-items:center;justify-content:center;}

  
.push-types{width:660rpx;margin:0 auto;text-align:center;padding-bottom:40rpx;}


.push-type{background:none !important;border:0 none !important;border-radius:0;padding:0 !important;line-height:1em;}
.push-type::after{border:0 none;}

.push-icon{padding:30rpx 0;width:120rpx;display:block;margin:0 auto;background:#FFFFFF;overflow:hidden;border:1rpx solid #EEEEEE;border-radius:15rpx;}
.push-icon image{width:60rpx;height:60rpx;display:block;margin:0 auto;}
.push-text{font-size:22rpx;color:#666666;margin-top:20rpx;height:30rpx;line-height:30rpx;}
